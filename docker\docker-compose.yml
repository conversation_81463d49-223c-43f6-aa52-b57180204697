version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: runtime
    container_name: uav-management-app
    restart: unless-stopped
    ports:
      - "${APP_PORT:-8080}:8080"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Spring profiles
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-prod}

      # Database configuration
      SPRING_DATASOURCE_URL: jdbc:mysql://db:3306/${DB_NAME:-uav_management}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC&createDatabaseIfNotExist=true
      SPRING_DATASOURCE_USERNAME: ${DB_USER:-uav_user}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD:-secure_password}
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: com.mysql.cj.jdbc.Driver

      # JPA/Hibernate configuration
      SPRING_JPA_HIBERNATE_DDL_AUTO: ${DDL_AUTO:-update}
      SPRING_JPA_SHOW_SQL: ${SHOW_SQL:-false}
      SPRING_JPA_DATABASE_PLATFORM: org.hibernate.dialect.MySQL8Dialect

      # Redis configuration
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: ${REDIS_PASSWORD:-}

      # Application configuration
      SERVER_PORT: 8080
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: when_authorized

      # Logging
      LOGGING_LEVEL_COM_UAV: ${LOG_LEVEL:-INFO}
      LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_SECURITY: ${SECURITY_LOG_LEVEL:-WARN}

      # Security
      JWT_SECRET: ${JWT_SECRET:-your-very-secure-jwt-secret-key-change-in-production}

    volumes:
      - app-logs:/app/logs
      - app-config:/app/config
    networks:
      - uav-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  db:
    image: mysql:8.0
    container_name: uav-management-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root_password}
      MYSQL_DATABASE: ${DB_NAME:-uav_management}
      MYSQL_USER: ${DB_USER:-uav_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-secure_password}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - mysql-data:/var/lib/mysql
      - mysql-config:/etc/mysql/conf.d
      - ../db/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - uav-network
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --sql-mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD:-root_password}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  redis:
    image: redis:7-alpine
    container_name: uav-management-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - uav-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  nginx:
    image: nginx:alpine
    container_name: uav-management-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - uav-network
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'

networks:
  uav-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mysql-data:
    driver: local
  mysql-config:
    driver: local
  redis-data:
    driver: local
  app-logs:
    driver: local
  app-config:
    driver: local
  nginx-logs:
    driver: local
